Sep 10 09:32:24 box-installer python3[6280]: ERROR:infrastructure.external_apis.jetveo_client:Request on server failed with status 400 for /api/post-temperature-electronic
Sep 10 09:32:24 box-installer python3[6280]: INFO:infrastructure.external_apis.jetveo_client:Failed request saved to queue: /api/post-temperature-electronic
Sep 10 09:32:24 box-installer python3[6280]: INFO:infrastructure.external_apis.jetveo_client:Retrying failed requests
Sep 10 09:32:25 box-installer python3[6280]: ERROR:infrastructure.external_apis.jetveo_client:Request on server failed with status 400 for /api/post-temperature-electronic
Sep 10 09:32:25 box-installer python3[6280]: WARNING:infrastructure.external_apis.jetveo_client:Request 234 to /api/post-temperature-electronic marked as failed after 5 attempts
Sep 10 09:32:25 box-installer python3[6280]: INFO:     127.0.0.1:53300 - "GET /product/list HTTP/1.1" 200 OK
Sep 10 09:32:25 box-installer python3[6280]: ERROR:infrastructure.external_apis.jetveo_client:Request on server failed with status 400 for /api/post-temperature-electronic
Sep 10 09:32:26 box-installer python3[6280]: ERROR:infrastructure.external_apis.jetveo_client:Request on server failed with status 400 for /api/post-temperature-electronic
Sep 10 09:32:27 box-installer python3[6280]: ERROR:infrastructure.external_apis.jetveo_client:Request on server failed with status 400 for /api/post-temperature-electronic
Sep 10 09:32:27 box-installer python3[6280]: WARNING:managers.ws_manager:No WebSocket connection found for session: mqtt_open_1_1ec8e50d
Sep 10 09:32:27 box-installer python3[6280]: INFO:managers.session_manager:Created fsm_sequence session: 7e241821-9699-49e3-a01b-d513c3495113
Sep 10 09:32:27 box-installer python3[6280]: INFO:managers.session_manager:Started initial connection timeout for session 7e241821-9699-49e3-a01b-d513c3495113 (20s)
Sep 10 09:32:27 box-installer python3[6280]: INFO:managers.sequence_manager:Started FSM sequence 7e241821-9699-49e3-a01b-d513c3495113 with 1 sections
Sep 10 09:32:27 box-installer python3[6280]: INFO:hardware.electronics_api:Executing command (attempt 1/2): python3 /opt/backend-app/hardware/boardctl.py -d /dev/ttyS1 -a enable_led
Sep 10 09:32:27 box-installer python3[6280]: INFO:     127.0.0.1:53300 - "GET /product/list HTTP/1.1" 200 OK
Sep 10 09:32:27 box-installer python3[6280]: INFO:hardware.electronics_api:Command output - stdout:
Sep 10 09:32:27 box-installer python3[6280]: Script finished successfuly
Sep 10 09:32:27 box-installer python3[6280]: 0
Sep 10 09:32:27 box-installer python3[6280]: INFO:hardware.locker_control:LED system enabled
Sep 10 09:32:27 box-installer python3[6280]: INFO:hardware.electronics_api:Executing command (attempt 1/2): python3 /opt/backend-app/hardware/boardctl.py -d /dev/ttyS1 -a write_led_colors 1 A
Sep 10 09:32:27 box-installer python3[6280]: INFO:hardware.electronics_api:Command output - stdout:
Sep 10 09:32:27 box-installer python3[6280]: Script finished successfuly
Sep 10 09:32:27 box-installer python3[6280]: 1
Sep 10 09:32:27 box-installer python3[6280]: INFO:hardware.locker_control:Set multiple LED colors: {1: 'A'}
Sep 10 09:32:27 box-installer python3[6280]: WARNING:managers.ws_manager:No WebSocket connection found for session: 7e241821-9699-49e3-a01b-d513c3495113
Sep 10 09:32:27 box-installer python3[6280]: WARNING:managers.ws_manager:No WebSocket connection found for session: 7e241821-9699-49e3-a01b-d513c3495113
Sep 10 09:32:27 box-installer python3[6280]: WARNING:managers.ws_manager:No WebSocket connection found for session: 7e241821-9699-49e3-a01b-d513c3495113
Sep 10 09:32:27 box-installer python3[6280]: INFO:hardware.electronics_api:Executing command (attempt 1/2): python3 /opt/backend-app/hardware/boardctl.py -d /dev/ttyS1 -a unlock_tempered 1
Sep 10 09:32:27 box-installer python3[6280]: INFO:hardware.electronics_api:Command output - stdout:
Sep 10 09:32:27 box-installer python3[6280]: Script finished successfuly
Sep 10 09:32:27 box-installer python3[6280]: 1
Sep 10 09:32:27 box-installer python3[6280]: INFO:hardware.locker_control:Successfully unlocked locker 1
Sep 10 09:32:27 box-installer python3[6280]: INFO:managers.timeline_logger:Logged event: open_lock for session None
Sep 10 09:32:27 box-installer python3[6280]: WARNING:managers.sequence_manager:No endpoint_type found for session 7e241821-9699-49e3-a01b-d513c3495113, skipping handle_section_open call
Sep 10 09:32:27 box-installer python3[6280]: INFO:hardware.electronics_api:Executing command (attempt 1/2): python3 /opt/backend-app/hardware/boardctl.py -d /dev/ttyS1 -a write_led_colors 1 B
Sep 10 09:32:27 box-installer python3[6280]: INFO:hardware.electronics_api:Command output - stdout:
Sep 10 09:32:27 box-installer python3[6280]: Script finished successfuly
Sep 10 09:32:27 box-installer python3[6280]: 1
Sep 10 09:32:27 box-installer python3[6280]: INFO:hardware.locker_control:Set multiple LED colors: {1: 'B'}
Sep 10 09:32:27 box-installer python3[6280]: WARNING:managers.ws_manager:No WebSocket connection found for session: 7e241821-9699-49e3-a01b-d513c3495113
Sep 10 09:32:27 box-installer python3[6280]: WARNING:managers.ws_manager:No WebSocket connection found for session: 7e241821-9699-49e3-a01b-d513c3495113
Sep 10 09:32:27 box-installer python3[6280]: INFO:managers.sequence_manager:Waiting for door 1 open, timeout in 59 seconds
Sep 10 09:32:27 box-installer python3[6280]: WARNING:managers.ws_manager:No WebSocket connection found for session: 7e241821-9699-49e3-a01b-d513c3495113
Sep 10 09:32:27 box-installer python3[6280]: INFO:hardware.electronics_api:Executing command (attempt 1/2): python3 /opt/backend-app/hardware/boardctl.py -d /dev/ttyS1 -a read_lock_state 1
Sep 10 09:32:27 box-installer python3[6280]: INFO:hardware.electronics_api:Command output - stdout:
Sep 10 09:32:27 box-installer python3[6280]: Script finished successfuly
Sep 10 09:32:27 box-installer python3[6280]: 1 0
Sep 10 09:32:27 box-installer python3[6280]: INFO:mqtt.client:Published to devices/123456789-1/responses/electronic/section_open: {'success': True, 'request_uuid': '1ec8e50d-e612-4e2c-ae11-4b44bdd20460', 'section_id': 1, 'message': 'Section 1 opening sequence started successfully', 'session_id': 'mqtt_open_1_1ec8e50d'}
Sep 10 09:32:27 box-installer python3[6280]: INFO:mqtt.client:Responded on devices/123456789-1/responses/electronic/section_open: {'success': True, 'request_uuid': '1ec8e50d-e612-4e2c-ae11-4b44bdd20460', 'section_id': 1, 'message': 'Section 1 opening sequence started successfully', 'session_id': 'mqtt_open_1_1ec8e50d'}
Sep 10 09:32:28 box-installer python3[6280]: ERROR:infrastructure.external_apis.jetveo_client:Request on server failed with status 400 for /api/post-temperature-electronic
Sep 10 09:32:28 box-installer python3[6280]: ERROR:infrastructure.external_apis.jetveo_client:Request on server failed with status 400 for /api/post-temperature-electronic
Sep 10 09:32:29 box-installer python3[6280]: {'serial_number': '123456789-1', 'timestamp': '2025-09-10T07:31:25.251134Z', 'sensors': [{'id': '4', 'value': 25}, {'id': '5', 'value': 25}]}
Sep 10 09:32:29 box-installer python3[6280]: {'serial_number': '123456789-1', 'timestamp': '2025-09-10T07:32:23.447268Z', 'sensors': [{'id': '4', 'value': 25}, {'id': '5', 'value': 25}]}
Sep 10 09:32:29 box-installer python3[6280]: INFO:     127.0.0.1:53300 - "GET /product/list HTTP/1.1" 200 OK
Sep 10 09:32:31 box-installer python3[6280]: INFO:     127.0.0.1:53300 - "GET /product/list HTTP/1.1" 200 OK
Sep 10 09:32:33 box-installer python3[6280]: INFO:     127.0.0.1:53300 - "GET /product/list HTTP/1.1" 200 OK
Sep 10 09:32:35 box-installer python3[6280]: INFO:     127.0.0.1:53300 - "GET /product/list HTTP/1.1" 200 OK
Sep 10 09:32:37 box-installer python3[6280]: INFO:     127.0.0.1:53300 - "GET /product/list HTTP/1.1" 200 OK
Sep 10 09:32:39 box-installer python3[6280]: INFO:     127.0.0.1:53300 - "GET /product/list HTTP/1.1" 200 OK
Sep 10 09:32:41 box-installer python3[6280]: INFO:     127.0.0.1:53300 - "GET /product/list HTTP/1.1" 200 OK
Sep 10 09:32:43 box-installer python3[6280]: INFO:     127.0.0.1:53300 - "GET /product/list HTTP/1.1" 200 OK
Sep 10 09:32:45 box-installer python3[6280]: WARNING:managers.ws_manager:No WebSocket connection found for session: mqtt_open_3_e333986e
Sep 10 09:32:45 box-installer python3[6280]: INFO:managers.session_manager:Created fsm_sequence session: ae2c8fc6-a7b6-4acf-9bb2-e165eefc3cfe
Sep 10 09:32:45 box-installer python3[6280]: INFO:managers.session_manager:Started initial connection timeout for session ae2c8fc6-a7b6-4acf-9bb2-e165eefc3cfe (20s)
Sep 10 09:32:45 box-installer python3[6280]: INFO:managers.sequence_manager:Started FSM sequence ae2c8fc6-a7b6-4acf-9bb2-e165eefc3cfe with 1 sections
Sep 10 09:32:45 box-installer python3[6280]: INFO:hardware.electronics_api:Executing command (attempt 1/2): python3 /opt/backend-app/hardware/boardctl.py -d /dev/ttyS1 -a enable_led
Sep 10 09:32:45 box-installer python3[6280]: INFO:hardware.electronics_api:Command output - stdout:
Sep 10 09:32:45 box-installer python3[6280]: Script finished successfuly
Sep 10 09:32:45 box-installer python3[6280]: 0
Sep 10 09:32:45 box-installer python3[6280]: INFO:hardware.locker_control:LED system enabled
Sep 10 09:32:45 box-installer python3[6280]: INFO:hardware.electronics_api:Executing command (attempt 1/2): python3 /opt/backend-app/hardware/boardctl.py -d /dev/ttyS1 -a write_led_colors 3 A
Sep 10 09:32:45 box-installer python3[6280]: INFO:hardware.electronics_api:Command output - stdout:
Sep 10 09:32:45 box-installer python3[6280]: Script finished successfuly
Sep 10 09:32:45 box-installer python3[6280]: 1
Sep 10 09:32:45 box-installer python3[6280]: INFO:hardware.locker_control:Set multiple LED colors: {3: 'A'}
Sep 10 09:32:45 box-installer python3[6280]: WARNING:managers.ws_manager:No WebSocket connection found for session: ae2c8fc6-a7b6-4acf-9bb2-e165eefc3cfe
Sep 10 09:32:45 box-installer python3[6280]: WARNING:managers.ws_manager:No WebSocket connection found for session: ae2c8fc6-a7b6-4acf-9bb2-e165eefc3cfe
Sep 10 09:32:45 box-installer python3[6280]: WARNING:managers.ws_manager:No WebSocket connection found for session: ae2c8fc6-a7b6-4acf-9bb2-e165eefc3cfe
Sep 10 09:32:45 box-installer python3[6280]: INFO:hardware.electronics_api:Executing command (attempt 1/2): python3 /opt/backend-app/hardware/boardctl.py -d /dev/ttyS1 -a unlock_tempered 3
Sep 10 09:32:45 box-installer python3[6280]: INFO:hardware.electronics_api:Command output - stdout:
Sep 10 09:32:45 box-installer python3[6280]: Script finished successfuly
Sep 10 09:32:45 box-installer python3[6280]: 1
Sep 10 09:32:45 box-installer python3[6280]: INFO:hardware.locker_control:Successfully unlocked locker 3
Sep 10 09:32:45 box-installer python3[6280]: INFO:managers.timeline_logger:Logged event: open_lock for session None
Sep 10 09:32:45 box-installer python3[6280]: WARNING:managers.sequence_manager:No endpoint_type found for session ae2c8fc6-a7b6-4acf-9bb2-e165eefc3cfe, skipping handle_section_open call
Sep 10 09:32:45 box-installer python3[6280]: INFO:hardware.electronics_api:Executing command (attempt 1/2): python3 /opt/backend-app/hardware/boardctl.py -d /dev/ttyS1 -a write_led_colors 3 B
Sep 10 09:32:45 box-installer python3[6280]: INFO:hardware.electronics_api:Command output - stdout:
Sep 10 09:32:45 box-installer python3[6280]: Script finished successfuly
Sep 10 09:32:45 box-installer python3[6280]: 1
Sep 10 09:32:45 box-installer python3[6280]: INFO:hardware.locker_control:Set multiple LED colors: {3: 'B'}
Sep 10 09:32:45 box-installer python3[6280]: WARNING:managers.ws_manager:No WebSocket connection found for session: ae2c8fc6-a7b6-4acf-9bb2-e165eefc3cfe
Sep 10 09:32:45 box-installer python3[6280]: WARNING:managers.ws_manager:No WebSocket connection found for session: ae2c8fc6-a7b6-4acf-9bb2-e165eefc3cfe
Sep 10 09:32:45 box-installer python3[6280]: INFO:managers.sequence_manager:Waiting for door 3 open, timeout in 59 seconds
Sep 10 09:32:45 box-installer python3[6280]: WARNING:managers.ws_manager:No WebSocket connection found for session: ae2c8fc6-a7b6-4acf-9bb2-e165eefc3cfe
Sep 10 09:32:45 box-installer python3[6280]: INFO:hardware.electronics_api:Executing command (attempt 1/2): python3 /opt/backend-app/hardware/boardctl.py -d /dev/ttyS1 -a read_lock_state 3
Sep 10 09:32:45 box-installer python3[6280]: INFO:hardware.electronics_api:Command output - stdout:
Sep 10 09:32:45 box-installer python3[6280]: Script finished successfuly
Sep 10 09:32:45 box-installer python3[6280]: 1 0
Sep 10 09:32:45 box-installer python3[6280]: INFO:mqtt.client:Published to devices/123456789-1/responses/electronic/section_open: {'success': True, 'request_uuid': 'e333986e-30c3-4102-8426-5c6f43dd2f21', 'section_id': 3, 'message': 'Section 3 opening sequence started successfully', 'session_id': 'mqtt_open_3_e333986e'}
Sep 10 09:32:45 box-installer python3[6280]: INFO:mqtt.client:Responded on devices/123456789-1/responses/electronic/section_open: {'success': True, 'request_uuid': 'e333986e-30c3-4102-8426-5c6f43dd2f21', 'section_id': 3, 'message': 'Section 3 opening sequence started successfully', 'session_id': 'mqtt_open_3_e333986e'}
Sep 10 09:32:45 box-installer python3[6280]: INFO:     127.0.0.1:53300 - "GET /product/list HTTP/1.1" 200 OK
Sep 10 09:32:47 box-installer python3[6280]: INFO:     127.0.0.1:53300 - "GET /product/list HTTP/1.1" 200 OK
Sep 10 09:32:49 box-installer python3[6280]: INFO:     127.0.0.1:53300 - "GET /product/list HTTP/1.1" 200 OK
Sep 10 09:32:51 box-installer python3[6280]: INFO:     127.0.0.1:53300 - "GET /product/list HTTP/1.1" 200 OK
Sep 10 09:32:53 box-installer python3[6280]: INFO:     127.0.0.1:53300 - "GET /product/list HTTP/1.1" 200 OK
Sep 10 09:32:55 box-installer python3[6280]: INFO:     127.0.0.1:53300 - "GET /product/list HTTP/1.1" 200 OK
Sep 10 09:32:57 box-installer python3[6280]: INFO:     127.0.0.1:53300 - "GET /product/list HTTP/1.1" 200 OK
Sep 10 09:32:59 box-installer python3[6280]: INFO:     127.0.0.1:53300 - "GET /product/list HTTP/1.1" 200 OK
Sep 10 09:33:01 box-installer python3[6280]: INFO:     127.0.0.1:53300 - "GET /product/list HTTP/1.1" 200 OK
Sep 10 09:33:03 box-installer python3[6280]: INFO:     127.0.0.1:53300 - "GET /product/list HTTP/1.1" 200 OK
Sep 10 09:33:05 box-installer python3[6280]: INFO:     127.0.0.1:53300 - "GET /product/list HTTP/1.1" 200 OK
Sep 10 09:33:07 box-installer python3[6280]: INFO:     127.0.0.1:53300 - "GET /product/list HTTP/1.1" 200 OK
Sep 10 09:33:09 box-installer python3[6280]: INFO:     127.0.0.1:53300 - "GET /product/list HTTP/1.1" 200 OK
Sep 10 09:33:11 box-installer python3[6280]: INFO:     127.0.0.1:53300 - "GET /product/list HTTP/1.1" 200 OK
Sep 10 09:33:13 box-installer python3[6280]: INFO:     127.0.0.1:53300 - "GET /product/list HTTP/1.1" 200 OK
Sep 10 09:33:15 box-installer python3[6280]: INFO:     127.0.0.1:53300 - "GET /product/list HTTP/1.1" 200 OK
Sep 10 09:33:17 box-installer python3[6280]: INFO:     127.0.0.1:53300 - "GET /product/list HTTP/1.1" 200 OK
Sep 10 09:33:19 box-installer python3[6280]: INFO:     127.0.0.1:53300 - "GET /product/list HTTP/1.1" 200 OK
Sep 10 09:33:21 box-installer python3[6280]: INFO:     127.0.0.1:53300 - "GET /product/list HTTP/1.1" 200 OK
Sep 10 09:33:23 box-installer python3[6280]: INFO:     127.0.0.1:53300 - "GET /product/list HTTP/1.1" 200 OK
Sep 10 09:33:25 box-installer python3[6280]: INFO:     127.0.0.1:53300 - "GET /product/list HTTP/1.1" 200 OK
Sep 10 09:33:26 box-installer python3[6280]: INFO:infrastructure.external_apis.jetveo_client:Request on server send succesfully: {'success': True, 'message': 'Success'} for /api/imalive
Sep 10 09:33:26 box-installer python3[6280]: ERROR:infrastructure.external_apis.jetveo_client:Request on server failed with status 400 for /api/post-temperature-electronic
Sep 10 09:33:26 box-installer python3[6280]: INFO:infrastructure.external_apis.jetveo_client:Failed request saved to queue: /api/post-temperature-electronic
Sep 10 09:33:26 box-installer python3[6280]: INFO:infrastructure.external_apis.jetveo_client:Retrying failed requests
Sep 10 09:33:27 box-installer python3[6280]: {'serial_number': '123456789-1', 'timestamp': '2025-09-10T07:33:26.307244Z', 'sensors': [{'id': '4', 'value': 25}, {'id': '5', 'value': 25}]}
Sep 10 09:33:27 box-installer python3[6280]: INFO:     127.0.0.1:53300 - "GET /product/list HTTP/1.1" 200 OK
Sep 10 09:33:28 box-installer python3[6280]: ERROR:infrastructure.external_apis.jetveo_client:Request on server failed with status 400 for /api/post-temperature-electronic
Sep 10 09:33:28 box-installer python3[6280]: WARNING:infrastructure.external_apis.jetveo_client:Request 235 to /api/post-temperature-electronic marked as failed after 5 attempts
Sep 10 09:33:29 box-installer python3[6280]: ERROR:infrastructure.external_apis.jetveo_client:Request on server failed with status 400 for /api/post-temperature-electronic
Sep 10 09:33:29 box-installer python3[6280]: {'serial_number': '123456789-1', 'timestamp': '2025-09-10T07:29:11.791604Z', 'sensors': [{'id': '4', 'value': 25}, {'id': '5', 'value': 25}]}
Sep 10 09:33:29 box-installer python3[6280]: {'serial_number': '123456789-1', 'timestamp': '2025-09-10T07:30:14.172198Z', 'sensors': [{'id': '4', 'value': 25}, {'id': '5', 'value': 25}]}
Sep 10 09:33:29 box-installer python3[6280]: INFO:     127.0.0.1:53300 - "GET /product/list HTTP/1.1" 200 OK
Sep 10 09:33:29 box-installer python3[6280]: ERROR:infrastructure.external_apis.jetveo_client:Request on server failed with status 400 for /api/post-temperature-electronic
Sep 10 09:33:30 box-installer python3[6280]: ERROR:infrastructure.external_apis.jetveo_client:Request on server failed with status 400 for /api/post-temperature-electronic
Sep 10 09:33:31 box-installer python3[6280]: ERROR:infrastructure.external_apis.jetveo_client:Request on server failed with status 400 for /api/post-temperature-electronic
Sep 10 09:33:31 box-installer python3[6280]: {'serial_number': '123456789-1', 'timestamp': '2025-09-10T07:31:16.809770Z', 'sensors': [{'id': '4', 'value': 25}, {'id': '5', 'value': 25}]}
Sep 10 09:33:31 box-installer python3[6280]: {'serial_number': '123456789-1', 'timestamp': '2025-09-10T07:31:25.251134Z', 'sensors': [{'id': '4', 'value': 25}, {'id': '5', 'value': 25}]}
Sep 10 09:33:31 box-installer python3[6280]: {'serial_number': '123456789-1', 'timestamp': '2025-09-10T07:32:23.447268Z', 'sensors': [{'id': '4', 'value': 25}, {'id': '5', 'value': 25}]}
Sep 10 09:33:31 box-installer python3[6280]: INFO:     127.0.0.1:53300 - "GET /product/list HTTP/1.1" 200 OK
Sep 10 09:33:31 box-installer python3[6280]: ERROR:infrastructure.external_apis.jetveo_client:Request on server failed with status 400 for /api/post-temperature-electronic
Sep 10 09:33:33 box-installer python3[6280]: {'serial_number': '123456789-1', 'timestamp': '2025-09-10T07:33:26.307244Z', 'sensors': [{'id': '4', 'value': 25}, {'id': '5', 'value': 25}]}
Sep 10 09:33:33 box-installer python3[6280]: INFO:     127.0.0.1:53300 - "GET /product/list HTTP/1.1" 200 OK
Sep 10 09:33:35 box-installer python3[6280]: INFO:     127.0.0.1:53300 - "GET /product/list HTTP/1.1" 200 OK
Sep 10 09:33:37 box-installer python3[6280]: INFO:     127.0.0.1:53300 - "GET /product/list HTTP/1.1" 200 OK
Sep 10 09:33:39 box-installer python3[6280]: INFO:     127.0.0.1:53300 - "GET /product/list HTTP/1.1" 200 OK
