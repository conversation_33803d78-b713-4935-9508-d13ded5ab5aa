"""
Product Repository - Database operations for product management.
Handles all database interactions for product operations.
"""

import logging
import mysql.connector
from typing import Dict, Optional, List, Any
from datetime import datetime
from uuid import uuid4
from decimal import Decimal
from os import getenv
from dotenv import load_dotenv
import random
from managers.timeline_logger import log_timeline_event
from infrastructure.external_apis.jetveo_client import product_change_status_async
from infrastructure.external_apis.jetveo_client import product_change_status_async

load_dotenv()

logger = logging.getLogger(__name__)

class ProductRepository:
    """Repository for product database operations"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def _get_db_connection(self):
        """Get database connection"""
        return mysql.connector.connect(
            host=getenv("DB_HOST"),
            port=int(getenv("DB_PORT")),
            database=getenv("DB_NAME"),
            user=getenv("DB_USER"),
            password=getenv("DB_PASSWORD")
        )
    
    async def find_product_for_pickup(
        self,
        section_id: Optional[int] = None,
        reservation_pin: Optional[str] = None,
        product_uuid: Optional[str] = None
    ) -> Optional[Dict[str, Any]]:
        """
        Find product for pickup by section_id, reservation_pin, or product_uuid.

        Args:
            section_id: Section ID to search in (customer pickup without PIN)
            reservation_pin: Reservation PIN to search by (customer pickup with PIN)
            product_uuid: Product UUID to search by

        Returns:
            Product record if found, None otherwise

        Note:
            - If product is reserved (reserved = 1), PIN is required
            - If product is not reserved, it can be picked up by section_id only
            - UUID search returns any product with status = 1
        """
        conn = self._get_db_connection()
        cursor = conn.cursor(dictionary=True)

        try:
            if product_uuid is not None:
                # Find by product UUID
                cursor.execute("""
                    SELECT * FROM sale_reservations
                    WHERE uuid = %s AND status = 1
                """, (product_uuid,))
            elif reservation_pin is not None:
                # Find by reservation_pin (customer pickup with PIN)
                cursor.execute("""
                    SELECT * FROM sale_reservations
                    WHERE reservation_pin = %s AND status = 1 AND reserved = 1
                """, (reservation_pin,))
            elif section_id is not None:
                # Find by section_id (customer pickup without PIN - only for non-reserved products)
                cursor.execute("""
                    SELECT * FROM sale_reservations
                    WHERE section_id = %s AND status = 1 AND reserved = 0
                    ORDER BY created_at DESC
                    LIMIT 1
                """, (str(section_id),))
            else:
                return None

            return cursor.fetchone()

        except mysql.connector.Error as err:
            self.logger.error(f"Database error finding product for pickup: {err}")
            return None
        finally:
            cursor.close()
            conn.close()
    
    async def find_reserved_product_in_section(self, section_id: int) -> Optional[Dict[str, Any]]:
        """
        Find reserved product in a specific section.
        
        Args:
            section_id: Section ID to search in
            
        Returns:
            Reserved product record if found, None otherwise
        """
        conn = self._get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        try:
            cursor.execute("""
                SELECT * FROM sale_reservations 
                WHERE section_id = %s AND status = 1 AND reserved = 1
                ORDER BY created_at DESC
                LIMIT 1
            """, (str(section_id),))
            
            return cursor.fetchone()
            
        except mysql.connector.Error as err:
            self.logger.error(f"Database error finding reserved product in section: {err}")
            return None
        finally:
            cursor.close()
            conn.close()
    
    async def check_section_has_active_product(self, section_id: int) -> bool:
        """
        Check if section already has an active product.
        
        Args:
            section_id: Section ID to check
            
        Returns:
            True if section has active product, False otherwise
        """
        conn = self._get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        try:
            cursor.execute("""
                SELECT id FROM sale_reservations 
                WHERE section_id = %s AND status != 0
            """, (section_id,))
            
            existing_product = cursor.fetchone()
            return existing_product is not None
            
        except mysql.connector.Error as err:
            self.logger.error(f"Database error checking section: {err}")
            return False
        finally:
            cursor.close()
            conn.close()
    
    async def insert_custom_product(
        self, 
        section_id: int, 
        price: Decimal
    ) -> Optional[Dict[str, Any]]:
        """
        Insert a custom product into database.
        
        Args:
            section_id: Section ID where product will be placed
            price: Product price
            
        Returns:
            Inserted product record or None if failed
        """
        conn = self._get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        try:
            # Generate new UUID
            new_uuid = str(uuid4())
            
            # Insert new product
            cursor.execute("""
                INSERT INTO sale_reservations 
                (uuid, section_id, price, status, type, created_at, last_update)
                VALUES (%s, %s, %s, 1, 'custom', %s, %s)
            """, (
                new_uuid,
                section_id,
                float(price),
                datetime.now(),
                datetime.now()
            ))
            
            conn.commit()
            
            product_change_status_async(
                reservation_uuid=new_uuid,
                section_id=section_id,
                price=float(price),
                action=1,
                status=1
            )


            # Get the inserted record
            cursor.execute("""
                SELECT * FROM sale_reservations 
                WHERE uuid = %s
            """, (new_uuid,))
            
            return cursor.fetchone()
            
        except mysql.connector.Error as err:
            conn.rollback()
            self.logger.error(f"Database error inserting custom product: {err}")
            return None
        finally:
            cursor.close()
            conn.close()
    
    async def insert_product_with_ean(
        self, 
        section_id: int, 
        ean: str,
        product_data: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """
        Insert a product with EAN code into database.
        
        Args:
            section_id: Section ID where product will be placed
            ean: EAN code of the product
            product_data: Product data from external API
            
        Returns:
            Inserted product record or None if failed
        """
        conn = self._get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        try:
            # Generate new UUID
            product_uuid = str(uuid4())
            
            # Insert new product into database with external API data
            cursor.execute("""
                INSERT INTO sale_reservations
                (uuid, section_id, ean, status, type, name, description, price,
                 age_control_required, cover_image, created_at, last_update)
                VALUES (%s, %s, %s, 1, 'ean', %s, %s, %s, %s, %s, %s, %s)
            """, (
                product_uuid,
                str(section_id),
                ean,
                product_data.get('name'),
                product_data.get('description'),
                product_data.get('price'),
                product_data.get('age_control_required'),
                product_data.get('cover_image'),
                datetime.now(),
                datetime.now()
            ))
            
            conn.commit()

            product_change_status_async(
                reservation_uuid=product_uuid,
                section_id=section_id,
                price=product_data.get('price'),
                ean=ean,
                action=1,
                status=1
            )
            
            # Get the inserted record
            cursor.execute("""
                SELECT * FROM sale_reservations 
                WHERE uuid = %s
            """, (product_uuid,))
            
            return cursor.fetchone()
            
        except mysql.connector.Error as err:
            conn.rollback()
            self.logger.error(f"Database error inserting product with EAN: {err}")
            return None
        finally:
            cursor.close()
            conn.close()
    
    async def remove_product(self, section_id: int) -> bool:
        """
        Remove product from section.
        Gets product data, updates status, and sends product_change_status.

        Args:
            section_id: Section ID to remove product from

        Returns:
            True if successful, False otherwise
        """
        conn = self._get_db_connection()
        cursor = conn.cursor(dictionary=True)

        try:
            # First, get the product data before updating
            cursor.execute("""
                SELECT uuid, section_id, price, ean
                FROM sale_reservations
                WHERE section_id = %s AND status != 0
            """, (section_id,))

            product = cursor.fetchone()

            if not product:
                self.logger.warning(f"No active product found in section {section_id}")
                return False

            # Update the product status
            cursor.execute("""
                UPDATE sale_reservations
                SET status = 0, last_update = %s
                WHERE section_id = %s AND status != 0
            """, (datetime.now(), section_id))

            if cursor.rowcount > 0:
                conn.commit()

                try:
                    product_change_status_async(
                        reservation_uuid=product['uuid'],
                        section_id=product['section_id'],
                        price=product.get('price'),
                        ean=product.get('ean'),
                        status=0,
                        action=2
                    )
                    self.logger.info(f"Removed product {product['uuid']} from section {section_id} and sent product_change_status")
                except Exception as e:
                    self.logger.error(f"Error sending product_change_status for product {product['uuid']}: {e}")

                return True
            else:
                self.logger.warning(f"No rows updated when removing product from section {section_id}")
                return False

        except mysql.connector.Error as err:
            conn.rollback()
            self.logger.error(f"Database error removing product: {err}")
            return False
        finally:
            cursor.close()
            conn.close()
    
    async def remove_all_products(self) -> bool:
        """
        Remove all products from all sections.
        Uses remove_product() for each section to ensure consistent behavior.

        Returns:
            True if successful, False otherwise
        """
        conn = self._get_db_connection()
        cursor = conn.cursor(dictionary=True)

        try:
            # Get all sections with active products
            cursor.execute("""
                SELECT DISTINCT section_id
                FROM sale_reservations
                WHERE status != 0
            """)
            sections_with_products = cursor.fetchall()

            removed_count = 0

            # Remove products from each section using remove_product()
            for section in sections_with_products:
                section_id = section['section_id']
                try:
                    success = await self.remove_product(section_id)
                    if success:
                        removed_count += 1
                        self.logger.debug(f"Removed product from section {section_id}")
                    else:
                        self.logger.warning(f"Failed to remove product from section {section_id}")
                except Exception as e:
                    self.logger.error(f"Error removing product from section {section_id}: {e}")
                    continue

            self.logger.info(f"Successfully removed products from {removed_count} sections")
            return True

        except mysql.connector.Error as err:
            self.logger.error(f"Database error getting sections for remove_all_products: {err}")
            return False
        finally:
            cursor.close()
            conn.close()
    
    async def list_products(self) -> List[Dict[str, Any]]:
        """
        List all active products in sections with mode = 'sale'.

        Returns:
            List of active products in sale sections only
        """
        conn = self._get_db_connection()
        cursor = conn.cursor(dictionary=True)

        try:
            cursor.execute("""
                SELECT sr.*
                FROM sale_reservations sr
                INNER JOIN box_sections bs ON sr.section_id = bs.section_id
                WHERE sr.status != 0 AND bs.mode = 'sale'
                ORDER BY sr.created_at DESC
            """)

            return cursor.fetchall()

        except mysql.connector.Error as err:
            self.logger.error(f"Database error listing products: {err}")
            return []
        finally:
            cursor.close()
            conn.close()


    async def find_reservations(
        self,
        product_uuid: Optional[str] = None,
        section_id: Optional[int] = None,
        status: Optional[int] = None,
        reserved: Optional[bool] = None,
        reservation_pin: Optional[str] = None,
        product_id: Optional[int] = None,
        ean: Optional[str] = None,
        price_min: Optional[float] = None,
        price_max: Optional[float] = None,
    ) -> List[Dict[str, Any]]:
        """
        Find product reservations based on various criteria.

        Args:
            product_uuid: Product UUID to search for
            section_id: Section ID to search in
            status: Product status (0=inactive, 1=active)
            reserved: Reservation status (True=reserved, False=not reserved)
            reservation_pin: Reservation PIN to search for
            product_id: Product ID to search for
            ean: EAN code to search for
            price_min: Minimum price filter
            price_max: Maximum price filter

        Returns:
            List of matching product reservations
        """
        conn = self._get_db_connection()
        cursor = conn.cursor(dictionary=True)

        try:
            # Build WHERE clause dynamically
            where_conditions = []
            params = []

            if product_uuid is not None:
                where_conditions.append("uuid = %s")
                params.append(product_uuid)

            if section_id is not None:
                where_conditions.append("section_id = %s")
                params.append(section_id)

            if status is not None:
                where_conditions.append("status = %s")
                params.append(status)

            if reserved is not None:
                where_conditions.append("reserved = %s")
                params.append(1 if reserved else 0)

            if reservation_pin is not None:
                where_conditions.append("reservation_pin = %s")
                params.append(reservation_pin)

            if product_id is not None:
                where_conditions.append("id = %s")
                params.append(product_id)

            if ean is not None:
                where_conditions.append("ean = %s")
                params.append(ean)

            if price_min is not None:
                where_conditions.append("price >= %s")
                params.append(float(price_min))

            if price_max is not None:
                where_conditions.append("price <= %s")
                params.append(float(price_max))

            # Build the query
            query = "SELECT * FROM sale_reservations"
            if where_conditions:
                query += " WHERE " + " AND ".join(where_conditions)
            query += " ORDER BY last_update DESC"

            cursor.execute(query, params)
            results = cursor.fetchall()

            self.logger.info(f"Found {len(results)} product reservations matching criteria")
            return results

        except mysql.connector.Error as err:
            self.logger.error(f"Database error finding reservations: {err}")
            return []
        finally:
            cursor.close()
            conn.close()


    async def edit_reservation(
        self,
        # Search parameters - at least one must be provided
        product_uuid: Optional[str] = None,
        section_id: Optional[int] = None,
        product_id: Optional[int] = None,
        reservation_pin: Optional[str] = None,

        # Update parameters - all optional
        status: Optional[int] = None,
        new_section_id: Optional[int] = None,
        price: Optional[float] = None,
        reserved: Optional[bool] = None,
        new_reservation_pin: Optional[str] = None,
        paid_status: Optional[bool] = None,
        ean: Optional[str] = None,
        name: Optional[str] = None,
        description: Optional[str] = None,
        quantity: Optional[int] = None,
        age_control_required: Optional[bool] = None,
        age_controlled: Optional[bool] = None,
        product_type: Optional[str] = None,
        cover_image: Optional[str] = None,
    ) -> Optional[Dict[str, Any]]:
        """
        Edit a product reservation. Combines functionality from:
        - reserve_section(): Set reservation status and PIN
        - update_payment_status(): Update payment status and deactivate
        - update_product_price(): Update product price
        - update_sale_reservation_status(): Update reservation status
        - cancel_reservation(): Cancel reservation

        Args:
            Search parameters (at least one required):
                product_uuid: Product UUID to find
                section_id: Section ID to find product in
                product_id: Product ID to find
                reservation_pin: Reservation PIN to find product by

            Update parameters (all optional):
                status: New status (0=inactive, 1=active)
                price: New price for the product
                reserved: Reservation status (True=reserve, False=cancel reservation)
                new_reservation_pin: New reservation PIN (use with reserved=True)
                paid_status: Payment status (True=paid, False=unpaid)
                And other product fields...

        Returns:
            Updated product record or None if failed
        """
        # First, find the reservation
        reservations = await self.find_reservations(
            product_uuid=product_uuid,
            section_id=section_id,
            product_id=product_id,
            reservation_pin=reservation_pin
        )

        if not reservations:
            search_criteria = []
            if product_uuid: search_criteria.append(f"product_uuid={product_uuid}")
            if section_id: search_criteria.append(f"section_id={section_id}")
            if product_id: search_criteria.append(f"product_id={product_id}")
            if reservation_pin: search_criteria.append(f"reservation_pin={reservation_pin}")

            self.logger.error(f"No product reservation found with criteria: {', '.join(search_criteria)}")
            return None

        # Use the first matching reservation
        existing_reservation = reservations[0]
        actual_product_id = existing_reservation['id']
        actual_section_id = existing_reservation['section_id']

        conn = self._get_db_connection()
        cursor = conn.cursor(dictionary=True)

        try:
            # Build the UPDATE query dynamically
            update_fields = []
            update_values = []

            if status is not None:
                update_fields.append("status = %s")
                update_values.append(status)

            if new_section_id is not None:
                update_fields.append("section_id = %s")
                update_values.append(new_section_id)

            if price is not None:
                update_fields.append("price = %s")
                update_values.append(float(price))

            if reserved is not None:
                update_fields.append("reserved = %s")
                update_values.append(1 if reserved else 0)

                # Handle reservation PIN logic
                if reserved and new_reservation_pin:
                    update_fields.append("reservation_pin = %s")
                    update_values.append(new_reservation_pin)
                elif reserved and not new_reservation_pin:
                    # Generate PIN if reserving without providing one
                    from .pin_generator import generate_pin
                    generated_pin = generate_pin()
                    if generated_pin:
                        update_fields.append("reservation_pin = %s")
                        update_values.append(generated_pin)
                elif not reserved:
                    # Clear PIN if cancelling reservation
                    update_fields.append("reservation_pin = NULL")

            elif new_reservation_pin is not None:
                # Update PIN without changing reserved status
                update_fields.append("reservation_pin = %s")
                update_values.append(new_reservation_pin)

            if paid_status is not None:
                update_fields.append("paid_status = %s")
                update_values.append('1' if paid_status else '0')

            if ean is not None:
                update_fields.append("ean = %s")
                update_values.append(ean)

            if name is not None:
                update_fields.append("name = %s")
                update_values.append(name)

            if description is not None:
                update_fields.append("description = %s")
                update_values.append(description)

            if quantity is not None:
                update_fields.append("quantity = %s")
                update_values.append(quantity)

            if age_control_required is not None:
                update_fields.append("age_control_required = %s")
                update_values.append(1 if age_control_required else 0)

            if age_controlled is not None:
                update_fields.append("age_controlled = %s")
                update_values.append(1 if age_controlled else 0)

            if product_type is not None:
                update_fields.append("type = %s")
                update_values.append(product_type)

            if cover_image is not None:
                update_fields.append("cover_image = %s")
                update_values.append(cover_image)

            # Always update last_update timestamp
            update_fields.append("last_update = %s")
            update_values.append(datetime.now())

            if len(update_fields) == 1:  # Only last_update field
                self.logger.error("No fields provided for update")
                return None

            # Execute the update
            query = f"UPDATE sale_reservations SET {', '.join(update_fields)} WHERE id = %s"
            update_values.append(actual_product_id)

            cursor.execute(query, update_values)
            conn.commit()

            if cursor.rowcount == 0:
                self.logger.error("No rows were updated")
                return None

            # Get the updated record
            cursor.execute("SELECT * FROM sale_reservations WHERE id = %s", (actual_product_id,))
            updated_reservation = cursor.fetchone()

            # Send notifications to Jetveo based on what was updated
            if updated_reservation:
                jetveo_params = {
                    'reservation_uuid': updated_reservation['uuid'],
                    'section_id': int(actual_section_id)
                }

                if price is not None:
                    jetveo_params['price'] = float(price)
                if reserved is not None:
                    jetveo_params['reserved'] = reserved
                    if reserved and (new_reservation_pin or 'reservation_pin' in [f.split(' = ')[0] for f in update_fields]):
                        jetveo_params['reservation_pin'] = updated_reservation.get('reservation_pin')
                if status is not None:
                    jetveo_params['status'] = status

                product_change_status_async(**jetveo_params)

            # Log timeline events based on operation type
            operation_type = "general_update"
            if price is not None:
                operation_type = "price_update"
                log_timeline_event(
                    event_type="product_price_update",
                    event_result="success",
                    message=f"Product price updated to {price}",
                    section_id=str(actual_section_id),
                    mode="product"
                )
            elif reserved is True:
                operation_type = "reservation"
                log_timeline_event(
                    event_type="product_reserved",
                    event_result="success",
                    message=f"Product reserved with PIN {updated_reservation.get('reservation_pin', 'N/A')}",
                    section_id=str(actual_section_id),
                    mode="product"
                )
            elif reserved is False:
                operation_type = "cancellation"
                log_timeline_event(
                    event_type="reservation_cancelled",
                    event_result="success",
                    message="Product reservation cancelled",
                    section_id=str(actual_section_id),
                    mode="product"
                )
            elif paid_status is True:
                operation_type = "payment_update"
                log_timeline_event(
                    event_type="product_payment_status",
                    event_result="paid",
                    message="Product payment status updated to paid",
                    section_id=str(actual_section_id),
                    mode="product"
                )
            elif status == 0:
                operation_type = "deactivation"
                log_timeline_event(
                    event_type="product_status_changed",
                    event_result="sold",
                    message="Product deactivated/sold",
                    section_id=str(actual_section_id),
                    mode="product"
                )

            self.logger.info(f"Updated product reservation ({operation_type}): ID={actual_product_id}, updated_fields={len(update_fields)-1}")
            return updated_reservation

        except mysql.connector.Error as err:
            conn.rollback()
            self.logger.error(f"Database error editing product reservation: {err}")
            return None
        finally:
            cursor.close()
            conn.close()


    async def reserve_section(
        self,
        section_id: int,
        reservation_pin: Optional[str] = None
    ) -> Optional[Dict[str, Any]]:
        """
        Reserve a section with optional PIN.
        This is a wrapper around edit_reservation for backward compatibility.

        Args:
            section_id: Section ID to reserve
            reservation_pin: Optional reservation PIN

        Returns:
            Updated product record or None if failed
        """
        return await self.edit_reservation(
            section_id=section_id,
            reserved=True,
            new_reservation_pin=reservation_pin
        )
    
    # purchase_product method removed - no longer needed
    # Remaining purchase_product method code removed
    
    async def update_payment_status(self, section_id: int) -> bool:
        """
        Update payment status for a product in section.
        After successful payment, set status to 0 (deactivated).
        This is a wrapper around edit_reservation for backward compatibility.

        Args:
            section_id: Section ID to update payment status for

        Returns:
            True if successful, False otherwise
        """
        result = await self.edit_reservation(
            section_id=section_id,
            paid_status=True,
            status=0
        )
        return result is not None

    async def update_product_price(
        self,
        section_id: int,
        new_price: Decimal
    ) -> Optional[Dict[str, Any]]:
        """
        Update the price of a product in a specific section.
        This is a wrapper around edit_reservation for backward compatibility.

        Args:
            section_id: Section ID containing the product
            new_price: New price for the product

        Returns:
            Updated product record with old price or None if failed
        """
        # Get old price first for backward compatibility
        reservations = await self.find_reservations(section_id=section_id, status=1)
        if not reservations:
            self.logger.error(f"No active product found in section {section_id}")
            return None

        old_price = reservations[0].get('price', 0)

        result = await self.edit_reservation(
            section_id=section_id,
            price=float(new_price)
        )

        if result:
            result['old_price'] = old_price  # Add old price for backward compatibility

        return result

    def update_sale_reservation_status(self, status: int, product_id: Optional[int] = None, section_id: Optional[int] = None) -> bool:
        """
        Update sale reservation status for a product.
        Used to mark product as picked up (status=0).
        This is a wrapper around edit_reservation for backward compatibility.

        Args:
            status: New status (0 = completed/picked up)
            product_id: Product ID to update (optional)
            section_id: Section ID containing the product (optional)

        Note:
            Either section_id or product_id must be provided, but not both.

        Returns:
            True if successful, False otherwise
        """
        # Validate input parameters
        if not product_id and not section_id:
            logger.error("Either product_id or section_id must be provided")
            return False

        if product_id and section_id:
            logger.error("Only one of product_id or section_id should be provided, not both")
            return False

        # Use asyncio.run to call the async function
        import asyncio
        try:
            result = asyncio.run(self.edit_reservation(
                product_id=product_id,
                section_id=section_id,
                status=status
            ))
            return result is not None
        except Exception as e:
            self.logger.error(f"Error updating sale reservation status: {e}")
            return False

    async def cancel_reservation(
        self, 
        section_id: int
    ) -> Optional[Dict[str, Any]]:
        """
        Cancel a reservation for a section.
        
        Args:
            section_id: Section ID to cancel reservation for
            
        Returns:
            Updated product record with cancelled PIN or None if failed
        """
        conn = self._get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        try:
            # First, check if there's a reserved product in the section
            cursor.execute("""
                SELECT id, reservation_pin FROM sale_reservations 
                WHERE section_id = %s AND status = 1 AND reserved = 1
            """, (section_id,))
            
            existing_product = cursor.fetchone()
            if not existing_product:
                self.logger.error(f"No reserved product found in section {section_id}")
                return None
            
            cancelled_pin = existing_product['reservation_pin']
            
            # Cancel the reservation
            cursor.execute("""
                UPDATE sale_reservations 
                SET reserved = 0, reservation_pin = NULL, last_update = %s
                WHERE section_id = %s AND status = 1 AND reserved = 1
            """, (datetime.now(), section_id))
            
            conn.commit()
            
            if cursor.rowcount > 0:
                # Get the updated record
                cursor.execute("""
                    SELECT * FROM sale_reservations 
                    WHERE section_id = %s AND status = 1
                """, (section_id,))
                
                result = cursor.fetchone()
                result['cancelled_pin'] = cancelled_pin  # Add cancelled PIN to result
                
                self.logger.info(f"Successfully cancelled reservation in section {section_id}, PIN: {cancelled_pin}")
                return result
            else:
                self.logger.error(f"No rows updated when cancelling reservation in section {section_id}")
                return None
            
        except mysql.connector.Error as err:
            conn.rollback()
            self.logger.error(f"Database error cancelling reservation: {err}")
            return None
        finally:
            cursor.close()
            conn.close()

    def get_product_sections(self) -> List[Dict[str, Any]]:
        """Get all product sections with availability status based on sale_reservations"""
        conn = self._get_db_connection()
        cursor = conn.cursor(dictionary=True)

        try:
            # Get all sections with mode = 'sale'
            cursor.execute("""
                SELECT section_id, identification_name, tempered, blocked, service,
                       mode, type, size_width, size_depth, size_height, size_category
                FROM box_sections
                WHERE visible = 1 AND mode = 'sale'
                ORDER BY section_id ASC
            """)
            sections = cursor.fetchall()

            # Check availability for each section based on sale_reservations
            for section in sections:
                section_id = section['section_id']
                section['is_available'] = self._check_product_section_availability(cursor, section_id)

            return sections

        except mysql.connector.Error as err:
            self.logger.error(f"Database error getting product sections: {err}")
            return []
        finally:
            cursor.close()
            conn.close()

    def _check_product_section_availability(self, cursor, section_id: int) -> bool:
        """Check if a product section is available (no active reservations in sale_reservations)"""
        try:
            cursor.execute("""
                SELECT id FROM sale_reservations
                WHERE section_id = %s AND status != 0
                LIMIT 1
            """, (section_id,))

            active_reservation = cursor.fetchone()
            return active_reservation is None

        except mysql.connector.Error as err:
            self.logger.error(f"Database error checking product section availability: {err}")
            return False

    async def handle_section_open(self, session_id: str, section_id: int, endpoint_type: str):
        """Handle section open for product endpoints"""
        match endpoint_type:
            case "product/pickup":
                return self.update_sale_reservation_status(status=0, section_id=section_id)     # deactivate product reservation
            case _:
                pass  # No action needed for other endpoints





# Global repository instance
product_repository = ProductRepository()
